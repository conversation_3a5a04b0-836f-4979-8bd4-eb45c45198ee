#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _masked_scale {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, double);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_masked_scale";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_masked_scale(Tensor self, Tensor mask, float scale) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & mask, double scale);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & mask, double scale);
};

struct TORCH_API _masked_scale_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Tensor &, double, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_masked_scale";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_masked_scale.out(Tensor self, Tensor mask, float scale, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Tensor & mask, double scale, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & mask, double scale, at::Tensor & out);
};

}} // namespace at::_ops
