#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>



#include <ATen/ops/_linalg_eigvals_ops.h>

namespace at {


// aten::_linalg_eigvals(Tensor self) -> Tensor
inline at::Tensor _linalg_eigvals(const at::Tensor & self) {
    return at::_ops::_linalg_eigvals::call(self);
}

}
