#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/hardsigmoid_meta.h>

namespace at {
namespace native {
struct TORCH_API structured_hardsigmoid_out : public at::meta::structured_hardsigmoid {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_hardsigmoid_out_mps : public at::meta::structured_hardsigmoid {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor hardsigmoid_quantized_cpu(const at::Tensor & self);
TORCH_API at::Tensor & hardsigmoid_out_quantized_cpu(const at::Tensor & self, at::Tensor & out);
} // namespace native
} // namespace at
