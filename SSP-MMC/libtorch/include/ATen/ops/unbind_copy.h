#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>



#include <ATen/ops/unbind_copy_ops.h>

namespace at {


// aten::unbind_copy.int(Tensor self, int dim=0) -> Tensor[]
inline ::std::vector<at::Tensor> unbind_copy(const at::Tensor & self, int64_t dim=0) {
    return at::_ops::unbind_copy_int::call(self, dim);
}

// aten::unbind_copy.int_out(Tensor self, int dim=0, *, Tensor(a!)[] out) -> ()
inline void unbind_copy_out(at::TensorList out, const at::Tensor & self, int64_t dim=0) {
    return at::_ops::unbind_copy_int_out::call(self, dim, out);
}
// aten::unbind_copy.int_out(Tensor self, int dim=0, *, Tensor(a!)[] out) -> ()
inline void unbind_copy_outf(const at::Tensor & self, int64_t dim, at::TensorList out) {
    return at::_ops::unbind_copy_int_out::call(self, dim, out);
}

}
