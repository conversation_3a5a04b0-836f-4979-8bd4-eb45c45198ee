#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>



#include <ATen/ops/copy_ops.h>

namespace at {


// aten::copy(Tensor self, Tensor src, bool non_blocking=False) -> Tensor
inline at::Tensor copy(const at::Tensor & self, const at::Tensor & src, bool non_blocking=false) {
    return at::_ops::copy::call(self, src, non_blocking);
}

// aten::copy.out(Tensor self, Tensor src, bool non_blocking=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & copy_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & src, bool non_blocking=false) {
    return at::_ops::copy_out::call(self, src, non_blocking, out);
}
// aten::copy.out(Tensor self, Tensor src, bool non_blocking=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & copy_outf(const at::Tensor & self, const at::Tensor & src, bool non_blocking, at::Tensor & out) {
    return at::_ops::copy_out::call(self, src, non_blocking, out);
}

}
