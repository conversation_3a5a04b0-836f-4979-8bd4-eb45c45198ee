import matplotlib.pyplot as plt
import numpy as np

# 示例数据（根据实际数据进行替换）
probability_of_recall = np.linspace(0.3, 1.0, 20)
number_of_samples_recall = np.random.randint(5000, 35000, size=20)
gru_hlr = np.random.rand(20)
gru_hlr_p = np.random.rand(20)
gru_hlr_t = np.random.rand(20)
gru_hlr_p_t = np.random.rand(20)
dhp = np.random.rand(20)
hlr = np.random.rand(20)
hlr_lex = np.random.rand(20)
pimsleur = np.random.rand(20)
leitner = np.random.rand(20)

half_life = np.logspace(0, 3.35, 20, base=2)
number_of_samples_half_life = np.random.randint(1000, 15000, size=20)
symm_absolute_percentage_error = np.random.rand(20) * 200

# 创建图表
fig, axs = plt.subplots(1, 2, figsize=(15, 7))

# 左图
ax1 = axs[0]
ax1_2 = ax1.twinx()
ax1.bar(probability_of_recall, number_of_samples_recall, color='gray', alpha=0.5)
ax1_2.plot(probability_of_recall, gru_hlr, label='GRU-HLR')
ax1_2.plot(probability_of_recall, gru_hlr_p, label='GRU-HLR-p')
ax1_2.plot(probability_of_recall, gru_hlr_t, label='GRU-HLR-t')
ax1_2.plot(probability_of_recall, gru_hlr_p_t, label='GRU-HLR-p-t')
ax1_2.plot(probability_of_recall, dhp, label='DHP')
ax1_2.plot(probability_of_recall, hlr, label='HLR')
ax1_2.plot(probability_of_recall, hlr_lex, label='HLR-lex')
ax1_2.plot(probability_of_recall, pimsleur, label='Pimsleur')
ax1_2.plot(probability_of_recall, leitner, label='Leitner')

ax1.set_xlabel('Probability of recall')
ax1.set_ylabel('Number of samples')
ax1_2.set_ylabel('Mean Absolute Error')
ax1.legend(loc='upper left')
ax1_2.legend(loc='upper right')

# 右图
ax2 = axs[1]
ax2_2 = ax2.twinx()
ax2.bar(half_life, number_of_samples_half_life, color='gray', alpha=0.5)
ax2.set_xscale('log')
ax2_2.plot(half_life, gru_hlr, label='GRU-HLR')
ax2_2.plot(half_life, gru_hlr_p, label='GRU-HLR-p')
ax2_2.plot(half_life, gru_hlr_t, label='GRU-HLR-t')
ax2_2.plot(half_life, gru_hlr_p_t, label='GRU-HLR-p-t')
ax2_2.plot(half_life, dhp, label='DHP')
ax2_2.plot(half_life, hlr, label='HLR')
ax2_2.plot(half_life, hlr_lex, label='HLR-lex')
ax2_2.plot(half_life, pimsleur, label='Pimsleur')
ax2_2.plot(half_life, leitner, label='Leitner')

ax2.set_xlabel('Half-life (days)')
ax2.set_ylabel('Number of samples')
ax2_2.set_ylabel('Symmetric Mean Absolute Percentage Error')
ax2.legend(loc='upper left')
ax2_2.legend(loc='upper right')

plt.tight_layout()
plt.show()
