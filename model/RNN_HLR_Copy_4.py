import torch.optim.lr_scheduler
import torch.nn as nn
import numpy as np
import torch.nn.functional
import matplotlib.pyplot as plt
import time
import math
import pandas as pd
from pathlib import Path
from sklearn.utils import shuffle
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm

from LPU import LPU
from dct import dct_channel_block


#定义损失函数，从pytorch中导入的库函数
def l1_loss(forecast, actual):#定义L1损失函数，L1:绝对值误差
    return nn.functional.l1_loss(forecast, actual)


def mse_loss(forecast, actual):#定义MSE损失函数，MSE:均方误差
    return nn.functional.mse_loss(forecast, actual)


def mape_loss(forecast, actual): #定义MAPE损失函数，MAPE:平均绝对百分比误差
    return nn.functional.l1_loss(forecast, actual) / abs(actual)

def smape_loss(forecast, actual):#定义sMAPE损失函数，sMAPE:对称平均绝对百分比误差，是对MAPE的改进
    return nn.functional.l1_loss(forecast, actual) / (abs(forecast) + abs(actual)) * 2


#定义时间函数，计算时间
def time_since(since):
    now = time.time()
    s = now - since
    m = math.floor(s / 60)
    s -= m * 60
    return '%dm %ds' % (m, s)

#添加了LPU模块，16特征进行输入
class RNN(nn.Module):
    def __init__(self, n_letters, n_hidden, n_categories, network): #n_letters:输入的特征数，n_hidden:隐藏层的神经元数，n_categories:输出的特征数, network:网络类型
        super().__init__()
        self.n_input = n_letters #输入的特征数
        self.n_hidden = n_hidden #隐藏层的神经元数
        self.n_out = n_categories #输出的特征数
        if network == 'GRU':
            self.rnn = nn.GRU(self.n_input, self.n_hidden, 1)
        self.fc = nn.Linear(self.n_hidden, self.n_out) #全连接层将输入特征映射到输出特征的维度,隐藏层的神经元个数是输入特征的维度，输出的特征数是输出特征的维度。
        #self.linear_out_len = nn.Linear(25, 25)
        #add new block_1
        #self.dct_layer = dct_channel_block(25)
        self.lpu = LPU(128)
        #add new block_2



    def forward(self, x, hx):
        x, h = self.rnn(x, hx=hx)  # x:输入数据和hx:隐藏层的状态
          # 输出是对样本的halflife的预测,是一个张量
        result = self.lpu(x.permute(1, 2, 0))
        #print(result.shape)
        result_1 = result.transpose(0, 3)[0]
        #print(result_1.shape)
        result_2 = result_1.permute(2, 0, 1)
        #print(result_2.shape)
        output = torch.exp(self.fc(result_2[-1]))
        #print(output.shape)
        return output, h

    #全连接层,它接受一个隐藏状态h，并通过全连接层fc得到输出。
    def full_connect(self, h):
        return self.fc(h)


class SpacedRepetitionModel(object):
    def __init__(self, train_set, test_set, omit_p_history=False, omit_t_history=False, hidden_nums=16, loss="MAPE",
                 network="GRU"): #train_set:训练集，test_set:测试集，omit_p_history:是否忽略p_history，omit_t_history:是否忽略t_history，hidden_nums:隐藏层的神经元数，loss:损失函数，network:网络类型

        self.n_hidden = hidden_nums
        self.omit_p = omit_p_history
        self.omit_t = omit_t_history
        if omit_p_history and omit_t_history: #如果忽略p_history和t_history
            self.feature_num = 1
        elif omit_p_history or omit_t_history: #如果忽略p_history或t_history
            self.feature_num = 2
        else:                                 #如果不忽略p_history和t_history
            self.feature_num = 3
        self.net_name = network
        self.net = RNN(self.feature_num, self.n_hidden, 1, network) #输入的特征数(取值3)，隐藏层的神经元数(取值16)，输出的特征数(取值1), 网络类型(取值GRU)
        self.lr = 1e-3 #学习率
        self.weight_decay = 1e-5 #权重衰减，防止过拟合
        self.optimizer = torch.optim.Adam(self.net.parameters(), lr=self.lr, weight_decay=self.weight_decay) #优化器，Adam优化器，学习率为lr(取值1e-3)，权重衰减为weight_decay(取值1e-5)
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(self.optimizer, T_max=32)#学习率调整策略，余弦退火调整学习率，最大迭代次数为32

        self.current_loss = 0 #当前损失
        self.avg_train_losses = [] #平均训练损失
        self.avg_eval_losses = [] #平均测试损失
        self.train_set = train_set #训练集
        self.test_set = test_set #测试集
        self.train_cnt = len(train_set) #训练集的长度
        self.n_iter = 1000000 #迭代样本总次数
        self.n_epoch = int(self.n_iter / self.train_cnt + 1) #轮数=迭代样本总次数/训练集的长度+1
        self.print_every = int(self.train_cnt / 4)  #每次打印的迭代次数，4表示每次迭代次数达到训练集的长度1/4时打印一次
        self.plot_every = self.train_cnt #每次绘图的迭代次数，每次迭代一次绘图一次
        self.loss_name = loss #损失函数的名称(取值MAPE)
        if loss == "MAPE":
            self.loss = mape_loss #损失函数,MAPE,平均绝对百分比误差
        elif loss == "L1":
            self.loss = l1_loss
        elif loss == "MSE":
            self.loss = mse_loss
        elif loss == "sMAPE":
            self.loss = smape_loss

        self.writer = SummaryWriter(comment=self.write_title()) #tensorboard的可视化

    def write_title(self): #返回一个标题字符串，定义标题，标题包括网络类型，隐藏层的神经元数，损失函数的名称，是否忽略p_history，是否忽略t_history，学习率，权重衰减，生成的标题字符串可能会在使用 TensorBoard 进行日志记录时使用，作为日志文件的名称或标识符。
        title = f'nn-{self.net_name}_nh-{self.n_hidden}_loss-{self.loss_name}'
        if self.omit_p:
            title += "-p"
        if self.omit_t:
            title += "-t"
        return title

    def train(self): #训练模型

        start = time.time() #开始时间，记录开始时间

        for i in range(self.n_epoch):#轮数n_epoch，每一轮都是对整个训练集进行一次迭代
            print(f"Epoch: {i + 1}")    #打印当前轮数
            train_set = shuffle(self.train_set, random_state=i) #打乱训练集的顺序，random_state=i表示每次打乱的顺序都是一样的
            for idx, index in enumerate(train_set.index):#对训练集的每一个样本(一行数据)进行迭代，index是样本的索引，idx是样本的序号，从0开始
                halflife, line, halflife_tensor, line_tensor, weight = self.sample2tensor(train_set.loc[index]) #将样本转换为张量，其中line是样本的特征，halflife是样本的halflife，halflife_tensor是样本的halflife的张量，line_tensor是样本的特征的张量，weight是样本的权重
                self.net.train()#训练模式，启用 BatchNormalization 和 Dropout
                self.optimizer.zero_grad()#梯度清零
                output, _ = self.net(line_tensor, None) #下划线变量_，它表示一个占位符，用于忽略不需要的返回值,line_tensor是输入数据的张量表示,通过调用self.net的__call__方法，将line_tensor作为输入传递给模型，并获取模型的输出输出结果是output,是对样本的halflife的预测,是一个张量
                #print(output)
                loss = self.loss(output[0], halflife_tensor) * weight #计算损失，预测值是output[0]，真实值是halflife_tensor，权重是weight
                #print(output[0])
                loss.backward() #反向传播
                self.optimizer.step() #更新参数
                self.scheduler.step() #更新学习率


                iterations = idx + i * self.train_cnt + 1 #表示目前已经迭代的样本个数，idx是样本的序号，i是轮数，self.train_cnt是训练集的长度

                if iterations % self.print_every == 0: #每迭代1/4数据集总样本数打印一次
                    guess = output.detach().numpy() #预测值，output是一个张量，通过detach()方法得到一个新的张量，numpy()方法将张量转换为numpy数组
                    correct = halflife_tensor[0] #真实值，halflife_tensor是一个张量，取第一个元素，得到一个标量，表示真实值
                    print(
                        '%d %d%% (%s) %.4f %.4f %.4f / %s' % ( #打印目前迭代样本数，迭代的百分比，已经经过的时间，损失值，预测值，真实值/样本的特征
                            iterations, iterations / (self.n_epoch * self.train_cnt) * 100, time_since(start),
                            loss.data.item(), guess, correct,
                            line))

                if iterations % self.plot_every == 0: #每完整迭代一次数据集就绘图一次
                    self.net.eval() #评估模式，不启用 BatchNormalization 和 Dropout
                    for stage in ('train', 'test'): #对训练集和测试集进行迭代
                        if stage == 'train': #如果是训练集
                            dataset = self.train_set #数据集是训练集
                        else:
                            dataset = self.test_set #数据集是测试集
                        plot_loss = 0 #绘图损失
                        plot_count = 0 #绘图计数
                        with torch.no_grad(): #不进行梯度计算
                            for plot_index in dataset.index: #对数据集的每一个样本进行迭代
                                halflife, line, halflife_tensor, line_tensor, weight = self.sample2tensor(
                                    dataset.loc[plot_index]) #将样本转换为张量
                                output, _ = self.net(line_tensor, None) #得到预测值
                                loss = self.loss(output[0], halflife_tensor) * weight #计算损失
                                plot_loss += loss.data.item() #累加损失,得到绘图损失
                                plot_count += 1 #绘图计数加1
                        if stage == 'train': #如果是训练集
                            self.avg_train_losses.append(plot_loss / plot_count) #将绘图损失除以绘图计数得到平均训练损失
                        else:
                            self.avg_eval_losses.append(plot_loss / plot_count) #将绘图损失除以绘图计数得到平均测试损失

                    print('Iteration %d %d%% (%s); Avg_train_loss %.4f; Avg_eval_loss %.4f' % ( #打印迭代次数，迭代的百分比，已经经过的时间，平均训练损失，平均测试损失
                        iterations, iterations / (self.n_epoch * self.train_cnt) * 100, time_since(start),
                        self.avg_train_losses[-1],
                        self.avg_eval_losses[-1]))

            self.writer.add_scalar('train_loss', self.avg_train_losses[-1], i + 1) #将平均训练损失写入tensorboard
            self.writer.add_scalar('eval_loss', self.avg_eval_losses[-1], i + 1) #将平均测试损失写入tensorboard


        title = self.write_title() #得到标题,标题包括网络类型，隐藏层的神经元数，损失函数的名称，是否忽略p_history，是否忽略t_history，学习率，权重衰减


        self.net.eval() #评估模式，不启用 BatchNormalization 和 Dropout
        path = f'./tmp/{title}' #路径
        Path(path).mkdir(parents=True, exist_ok=True) #创建路径
        torch.save(self.net, f'{path}/model.pth') #保存模型,模型的名称是model.pth,模型的路径是path
        #example_input = torch.rand(1, 1, self.feature_num) #生成一个随机张量，张量的维度是(1,1,3),3是输入的特征数,1是batch_size,1是序列长度
        #example_hidden = torch.rand(1, 1, self.n_hidden) #生成一个随机张量，张量的维度是(1,1,16),16是隐藏层的神经元数,1是batch_size,1是序列长度
        #fully_traced = torch.jit.trace_module(self.net, {'forward': (example_input, example_hidden),
                                                         #'full_connect': example_hidden})#将模型转换为torch脚本，fully_traced是一个脚本模块
        #fully_traced.save(f'{path}/model.pt') #保存脚本模块
        #self.writer.add_graph(self.net, [example_input, example_hidden]) #将模型的计算图写入tensorboard
        #self.writer.close() #关闭tensorboard

    def eval(self, repeat, fold): #评估模型,repeat:重复次数，fold:折数
        record = pd.DataFrame(
            columns=['r_history', 't_history', 'p_history',
                     't',
                     'halflife', 'hh', 'p', 'pp', 'ae', 'ape']) #创建一个空的DataFrame,列名分别是r_history, t_history, p_history, t, halflife, hh, p, pp, ae, ape,其中r_history, t_history, p_history是样本的特征，t是样本的delta_t，halflife是样本的halflife，hh是样本的halflife的预测值，p是样本的p_recall，pp是样本的p_recall的预测值，ae是样本的p_recall的预测值和真实值的绝对误差，ape是样本的halflife的预测值和真实值的绝对百分比误差
        self.net.eval() #评估模式，不启用 BatchNormalization 和 Dropout
        with torch.no_grad(): #不进行梯度计算
            ae = 0
            ape = 0
            count = 0
            for index in tqdm(self.test_set.index): #对测试集的每一个样本进行迭代,使用tqdm函数显示进度条
                sample = self.test_set.loc[index] #得到一个样本
                halflife, line, halflife_tensor, line_tensor, weight = self.sample2tensor(sample) #将样本转换为张量，其中line是样本的特征，halflife是样本的halflife，halflife_tensor是样本的halflife的张量，line_tensor是样本的特征的张量，weight是样本的权重
                output = self.net(line_tensor, None) #得到预测值，output是一个张量
                output = float(output[0]) #将张量转换为标量
                pp = np.exp(np.log(0.5) * sample['delta_t'] / output) #pp是一个标量，sample['delta_t']是样本的delta_t，output是样本的halflife的预测值，pp是样本的p_recall的预测值
                p = sample['p_recall'] #p是样本的p_recall
                ae += abs(p - pp) #累加绝对误差
                ape += abs(output - sample['halflife']) / sample['halflife'] #累加绝对百分比误差
                count += 1 #计数加1

                record = pd.concat([record, pd.DataFrame(#将样本的特征，样本的delta_t，样本的halflife，样本的halflife的预测值，样本的p_recall，样本的p_recall的预测值，样本的p_recall的预测值和真实值的绝对误差，样本的halflife的预测值和真实值的绝对百分比误差，构成一个DataFrame,并将这个DataFrame和record进行拼接
                    {'r_history': [sample['r_history']], #r_history是样本的r_history
                     't_history': [sample['t_history']], #t_history是样本的t_history
                     'p_history': [sample['p_history']], #p_history是样本的p_history
                     't': [sample['delta_t']], 'h': [sample['halflife']], #样本的delta_t，样本的halflife
                     'hh': [round(output, 2)], 'p': [sample['p_recall']], #样本的halflife的预测值，样本的p_recall
                     'pp': [round(pp, 3)], 'ae': [round(abs(p - pp), 3)], #样本的p_recall的预测值，样本的p_recall的预测值和真实值的绝对误差
                     'ape': [round(abs(output - sample['halflife']) / sample['halflife'], 3)]})],
                                   ignore_index=True) #round是一个内置函数，用于将一个数字四舍五入到指定的小数位数。它接受两个参数：number表示要进行四舍五入的数字，ndigits表示要保留的小数位数。ignore_index=True表示忽略索引
            print(f"model: gru") #打印模型类型
            print(f'sample num: {count}') #打印样本数
            print(f"mae: {ae / count}") #打印平均绝对误差
            print(f"mape: {ape / count}") #打印平均绝对百分比误差
            title = self.write_title() #得到标题,标题包括网络类型，隐藏层的神经元数，损失函数的名称，是否忽略p_history，是否忽略t_history，学习率，权重衰减
            path = f'./result/{title}' #路径
            Path(path).mkdir(parents=True, exist_ok=True) #创建路径
            record.to_csv(f'{path}/repeat{repeat}_fold{fold}_{int(time.time())}.tsv', index=False, sep='\t') #将record保存为tsv文件，文件名包括重复次数，折数，时间戳

    def sample2tensor(self, sample): #将样本转换为张量
        halflife = sample['halflife'] #halflife是样本的halflife
        features = [sample['r_history'], sample['t_history'], sample['p_history']] #features是样本的特征，包括r_history, t_history, p_history，是一个列表
        r_history = sample['r_history'].split(',') #r_history是样本的r_history,通过split方法将字符串转换为列表
        t_history = sample['t_history'].split(',') #t_history是样本的t_history,通过split方法将字符串转换为列表
        p_history = sample['p_history'].split(',') #p_history是样本的p_history,通过split方法将字符串转换为列表
        sample_tensor = torch.zeros(len(r_history), 1, self.feature_num) #生成一个全0的张量，张量的维度是(len(r_history), 1, self.feature_num),len(r_history)是r_history的长度，1是batch_size,self.feature_num(取值为3)是特征的维度

        for li, response in enumerate(r_history): #对r_history的每一个元素进行迭代，li是元素的序号，从0开始，response是元素的值
            sample_tensor[li][0][0] = int(response) #将r_history的每一个元素转换为整数，赋值给张量的第一个维度
            if self.omit_p and self.omit_t: #如果忽略p_history和t_history
                continue
            elif self.omit_t and not self.omit_p: #如果忽略t_history和不忽略p_history
                sample_tensor[li][0][1] = float(p_history[li]) 
            elif self.omit_p and not self.omit_t: #如果忽略p_history和不忽略t_history
                sample_tensor[li][0][1] = float(t_history[li])
            else:                                 #如果不忽略p_history和t_history
                sample_tensor[li][0][1] = float(t_history[li])
                sample_tensor[li][0][2] = float(p_history[li])

        halflife_tensor = torch.tensor([halflife], dtype=torch.float32) #将halflife转换为张量，张量的数据类型是torch.float32
        #print(halflife_tensor)
        #print(sample_tensor)
        return halflife, features, halflife_tensor, sample_tensor, sample[['weight_std']].values[0] #返回样本的halflife,样本的特征,样本的halflife的张量,样本的特征的张量,样本的权重
