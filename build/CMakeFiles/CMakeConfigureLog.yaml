
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:6 (project)"
    message: |
      The system is: Darwin - 24.1.0 - arm64
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      ld: library 'System' not found
      cc: error: linker command failed with exit code 1 (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/cc 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/3.31.6/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      ld: library 'c++' not found
      c++: error: linker command failed with exit code 1 (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /Library/Developer/CommandLineTools/usr/bin/c++ 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is AppleClang, found in:
        /Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/3.31.6/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO"
      binary: "/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_1230b/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_1230b.dir/build.make CMakeFiles/cmTC_1230b.dir/build
        Building C object CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o
        /Library/Developer/CommandLineTools/usr/bin/cc   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 16.0.0 (clang-1600.0.26.4)
        Target: arm64-apple-darwin24.1.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        cc: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.1 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 16.0.0 (clang-1600.0.26.4) default target arm64-apple-darwin24.1.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Library/Developer/CommandLineTools/usr/lib/clang/16/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_1230b
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1230b.dir/link.txt --verbose=1
        Apple clang version 16.0.0 (clang-1600.0.26.4)
        Target: arm64-apple-darwin24.1.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.1 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_1230b -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1115.7.3
        BUILD 07:38:57 Oct  4 2024
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.8)
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks
        /Library/Developer/CommandLineTools/usr/bin/cc  -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -o cmTC_1230b
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:113 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/16/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/16/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/16/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/Library/Developer/CommandLineTools/usr/lib/clang/16/include;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_1230b/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_1230b.dir/build.make CMakeFiles/cmTC_1230b.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/cc   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.4)]
        ignore line: [Target: arm64-apple-darwin24.1.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [cc: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.1 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-0EPNlO -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.4) default target arm64-apple-darwin24.1.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/16/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_1230b]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_1230b.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.4)]
        ignore line: [Target: arm64-apple-darwin24.1.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.1 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_1230b -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.1] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_1230b] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_1230b.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Library/Developer/CommandLineTools/usr/bin/ld
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the C compiler's linker: "/Library/Developer/CommandLineTools/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1115.7.3
      BUILD 07:38:57 Oct  4 2024
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.8)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ"
      binary: "/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_06630/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_06630.dir/build.make CMakeFiles/cmTC_06630.dir/build
        Building CXX object CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o
        /Library/Developer/CommandLineTools/usr/bin/c++   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Apple clang version 16.0.0 (clang-1600.0.26.4)
        Target: arm64-apple-darwin24.1.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        c++: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/usr/bin/../include/c++/v1"
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.1 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 16.0.0 (clang-1600.0.26.4) default target arm64-apple-darwin24.1.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1
         /Library/Developer/CommandLineTools/usr/lib/clang/16/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking CXX executable cmTC_06630
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_06630.dir/link.txt --verbose=1
        Apple clang version 16.0.0 (clang-1600.0.26.4)
        Target: arm64-apple-darwin24.1.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.1 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_06630 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1115.7.3
        BUILD 07:38:57 Oct  4 2024
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.8)
        Library search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks
        /Library/Developer/CommandLineTools/usr/bin/c++  -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_06630
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:113 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1]
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/16/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1]
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/16/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/16/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1;/Library/Developer/CommandLineTools/usr/lib/clang/16/include;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_06630/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_06630.dir/build.make CMakeFiles/cmTC_06630.dir/build]
        ignore line: [Building CXX object CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/c++   -arch arm64 -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.4)]
        ignore line: [Target: arm64-apple-darwin24.1.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [c++: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/usr/bin/../include/c++/v1"]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.1 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/16/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/PycharmProjects/Python-Project/SSP-MMC-Plus-main/SSP-MMC/cnpy/build/CMakeFiles/CMakeScratch/TryCompile-fQndUJ -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.4) default target arm64-apple-darwin24.1.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include/c++/v1]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/16/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_06630]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_06630.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.4)]
        ignore line: [Target: arm64-apple-darwin24.1.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.1 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_06630 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.1] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_06630] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_06630.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        linker tool for 'CXX': /Library/Developer/CommandLineTools/usr/bin/ld
        Library search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/usr/lib/swift]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX15.1.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the CXX compiler's linker: "/Library/Developer/CommandLineTools/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1115.7.3
      BUILD 07:38:57 Oct  4 2024
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.8)
...
